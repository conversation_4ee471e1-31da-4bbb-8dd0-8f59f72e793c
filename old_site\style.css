/* === RedFox Portfolio Pixel Art Theme === */

/* Color palette */
:root {
  --orange: #ff7f2a;
  --orange-light: #ff9900;
  --gray-dark: #222;
  --gray-medium: #444;
  --black: #000;
  --white: #f5f5f5;
  --accent: #fff;
}

/* Thème clair explicite sur body.light-theme */
body.light-theme {
  --orange: #ff7f2a;
  --orange-light: #ff8800;
  --gray-dark: #e8e8e8;
  --gray-medium: #d0d0d0;
  --black: #f8f8f8;
  --white: #333;
  --accent: #222;
  background: #eaeaea !important;
  background-image: linear-gradient(135deg, #eaeaea 0%, #d8d8d8 100%) !important;
  color: var(--white);
}

/* Pixel font (fallback monospace) */
body {
  font-family: Consolas, Menlo, Monaco, "Liberation Mono", "Courier New",
    Courier, monospace;
  background: linear-gradient(120deg, var(--gray-dark) 0%, #181818 100%);
  background-size: 200% 200%;
  background-position: 0% 50%;
  color: var(--white);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  box-sizing: border-box;
  animation: bg-gradient-move 5s cubic-bezier(0.77,0,0.18,1) infinite alternate;
  position: relative;
  overflow-x: hidden;
}

@keyframes bg-gradient-move {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

body > * {
  position: relative;
  z-index: 1;
}

/* Navbar */
.navbar {
  position: sticky;
  top: 0;
  background: var(--black);
  z-index: 100;
  border-bottom: 4px solid var(--orange);
  box-shadow: 0 2px 0 var(--gray-medium);
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.navbar ul {
  flex: 1;
  justify-content: center;
  display: flex;
  gap: 2rem;
  list-style: none;
  margin: 0;
  padding: 0.5rem 0;
  margin-left: 0.5rem;
}
.navbar a {
  color: var(--orange);
  text-decoration: none;
  font-weight: bold;
  font-size: 1.1rem;
  padding: 0.3em 0.8em;
  border-radius: 0;
  background: transparent;
  transition: background 0.18s, color 0.18s;
  border: none;
  box-shadow: none;
}
.navbar a:hover,
.navbar a:focus {
  background: var(--orange);
  color: var(--black);
  border: none;
  box-shadow: none;
  outline: none;
}

/* Sections */
.section {
  max-width: 900px;
  margin: 0 auto;
  padding: 4rem 1rem 3rem 1rem;
  border-bottom: 1px solid var(--gray-medium);
  background: var(--gray-dark);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
  border-radius: 0;
  margin-bottom: 2rem;
}
.section:last-child {
  border-bottom: none;
}

/* Accueil */
.accueil-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.2rem;
}
.mascotte-container {
  margin-bottom: 0.5rem;
}
#fox-mascot {
  image-rendering: pixelated;
  width: 96px;
  height: 96px;
  animation: fox-blink 4s infinite;
}
@keyframes fox-blink {
  0%,
  92%,
  100% {
    opacity: 1;
  }
  94%,
  98% {
    opacity: 0.7;
  }
}
h1 {
  font-size: 2.8rem;
  color: var(--orange);
  letter-spacing: 2px;
  text-shadow: 1px 1px 0 var(--black);
  margin: 0;
}
h2 {
  font-size: 1.5rem;
  color: var(--white);
  margin-bottom: 0.5rem;
}
.slogan {
  font-size: 1.1rem;
  color: var(--orange-light);
  background: var(--gray-medium);
  padding: 0.4em 1em;
  border-radius: 6px;
  box-shadow: none;
  font-style: italic;
}

/* À propos */
.about-content {
  display: flex;
  align-items: center;
  gap: 2rem;
}
.avatar-pixel {
  width: 64px;
  height: 64px;
  background: var(--gray-medium);
  border: 2px solid var(--orange-light);
  border-radius: 0;
  box-shadow: none;
}
@media (max-width: 600px) {
  .about-content {
    flex-direction: column;
    gap: 1rem;
  }
}

/* Compétences */
.skills-list {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  margin-top: 1.5rem;
}
.skill {
  display: flex;
  align-items: center;
  gap: 1.2rem;
}
.skill-name {
  min-width: 140px;
  font-weight: bold;
  color: var(--orange);
  text-shadow: 1px 1px 0 var(--black);
}
.skill-bar {
  flex: 1;
  height: 18px;
  background: var(--gray-medium);
  border: 2px solid var(--orange-light);
  border-radius: 0;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2px 0 var(--black);
}
.skill-bar::after {
  content: "";
  display: block;
  height: 100%;
  width: 0;
  background: linear-gradient(90deg, var(--orange), var(--orange-light));
  box-shadow: none;
  transition: width 1.5s cubic-bezier(0.65, 0, 0.35, 1);
  border-radius: 0;
  position: absolute;
  left: 0;
  top: 0;
}

.skill-bar.visible::after {
  animation: loadBar 1.5s forwards;
}

@keyframes loadBar {
  0% {
    width: 0;
  }
  100% {
    width: var(--target-width);
  }
}
/* Skill bar animation */
.skill-bar[data-level="95"] {
  --target-width: 95%;
}
.skill-bar[data-level="90"] {
  --target-width: 90%;
}
.skill-bar[data-level="85"] {
  --target-width: 85%;
}
.skill-bar[data-level="80"] {
  --target-width: 80%;
}
.skill-bar[data-level="75"] {
  --target-width: 75%;
}

/* Projets */
/* === Filtres des projets === */
.projects-filters {
  background: var(--gray-medium);
  border: 2px solid var(--orange);
  border-radius: 0;
  padding: 1.2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 0 var(--black);
}

.filter-search {
  margin-bottom: 1rem;
}

.search-input {
  width: 100%;
  max-width: 400px;
  font-family: inherit;
  font-size: 1rem;
  border: 2px solid var(--gray-dark);
  border-radius: 0;
  padding: 0.6em 1em;
  background: var(--black);
  color: var(--white);
  outline: none;
  transition: border-color 0.2s;
}

.search-input:focus {
  border-color: var(--orange-light);
}

.search-input::placeholder {
  color: var(--gray-medium);
  opacity: 0.8;
}



.filter-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.filter-results {
  flex: 1;
  text-align: left;
}

#results-count {
  color: var(--orange-light);
  font-style: italic;
  font-size: 0.9rem;
}

/* === Toggle d'affichage === */
.view-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.toggle-label {
  color: var(--orange-light);
  font-weight: bold;
  font-size: 0.9rem;
}

.view-toggle-btn {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  background: var(--gray-dark);
  color: var(--white);
  border: 2px solid var(--orange);
  border-radius: 0;
  padding: 0.4em 0.8em;
  font-family: inherit;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: bold;
}

.view-toggle-btn:hover {
  background: var(--orange);
  color: var(--black);
  border-color: var(--orange-light);
}

.view-toggle-btn[data-view="compact"] {
  background: var(--orange);
  color: var(--black);
  border-color: var(--orange-light);
  box-shadow: 0 2px 0 var(--black);
}

.toggle-icon {
  font-size: 1rem;
  line-height: 1;
}

.toggle-text {
  font-size: 0.85rem;
}

/* Classes utilitaires pour l'accessibilité */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Animation pour les projets filtrés */
.project-item, .project-card {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.project-item.filtered-out, .project-card.filtered-out {
  opacity: 0;
  transform: scale(0.95);
  pointer-events: none;
}

/* === Vue compacte des projets === */
.projects-list.compact-view {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
  max-width: 100%;
  overflow: hidden;
}

.projects-list.compact-view .project-item {
  background: var(--gray-medium);
  border: 2px solid var(--orange);
  border-radius: 0;
  padding: 0.8rem;
  transition: all 0.2s;
  cursor: pointer;
  min-width: 0; /* Permet au contenu de se rétrécir */
  box-sizing: border-box;
}

.projects-list.compact-view .project-item:hover {
  border-color: var(--orange-light);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.projects-list.compact-view .project-summary {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  position: static;
}

.projects-list.compact-view .project-summary:hover {
  background: none;
}

.projects-list.compact-view .project-pixelart {
  width: 24px;
  height: 24px;
  margin-right: 0.8rem;
  margin-bottom: 0.5rem;
}

.projects-list.compact-view .project-summary h3 {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  color: var(--orange);
}

.projects-list.compact-view .expand-arrow {
  display: none;
}

.projects-list.compact-view .project-details {
  display: none !important;
}

/* Masquer les infos compactes en vue détaillée */
.projects-list:not(.compact-view) .project-compact-info {
  display: none;
}

/* Afficher les infos compactes en vue compacte */
.projects-list.compact-view .project-compact-info {
  display: block;
}

.projects-list.compact-view .project-description {
  font-size: 0.85rem;
  color: var(--white);
  margin-bottom: 0.8rem;
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.projects-list.compact-view .project-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.8rem;
  font-size: 0.8rem;
}

.projects-list.compact-view .project-date {
  display: flex;
  gap: 0.3rem;
}

.projects-list.compact-view .date-label {
  color: var(--orange-light);
  font-weight: bold;
  min-width: auto;
}

.projects-list.compact-view .date-value {
  color: var(--white);
}

.projects-list.compact-view .more-info-btn {
  font-size: 0.8rem;
  padding: 0.3rem 0.6rem;
}

/* Vue détaillée (par défaut) */
.projects-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 2rem;
  width: 100%;
}

/* Breakpoint pour les très grands écrans */
@media (min-width: 1200px) {
  .projects-list.compact-view {
    grid-template-columns: repeat(3, 1fr);
    max-width: 100%;
  }
}

/* Breakpoint pour les écrans moyens-larges */
@media (max-width: 1199px) and (min-width: 901px) {
  .projects-list.compact-view {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

.project-item {
  display: flex;
  flex-direction: column;
  width: 100%;
  transition: all 0.3s ease;
}

.project-summary {
  display: flex;
  align-items: center;
  background: var(--gray-medium);
  border: 1.5px solid var(--orange);
  border-radius: 0;
  padding: 0.8rem 1rem;
  cursor: pointer;
  position: relative;
  z-index: 1;
  transition: background 0.15s, border-color 0.15s;
}

.project-summary:hover {
  border-color: var(--orange-light);
  background: var(--gray-dark);
}

.project-pixelart {
  width: 36px;
  height: 36px;
  background: var(--orange);
  border: 1.5px solid var(--orange-light);
  border-radius: 0;
  margin-right: 1rem;
  flex-shrink: 0;
}

.project-summary h3 {
  color: var(--orange);
  margin: 0;
  font-size: 1.1rem;
  text-shadow: none;
  flex-grow: 1;
}

.expand-arrow {
  color: var(--orange-light);
  font-size: 0.9rem;
  transition: transform 0.3s ease;
  display: block;
}

.project-summary.active .expand-arrow {
  transform: rotate(90deg);
}

.project-details {
  display: flex;
  background: var(--gray-dark);
  border: 1.5px solid var(--orange);
  border-top: none;
  padding: 1.2rem;
  gap: 1.5rem;
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease-out;
}

.project-details.active {
  opacity: 1;
  max-height: 300px;
  border-top: 1.5px solid var(--orange-light);
}

.project-image {
  width: 120px;
  height: 120px;
  background: var(--orange);
  border: 2px solid var(--orange-light);
  flex-shrink: 0;
}

.project-info {
  flex-grow: 1;
}

.project-info h3 {
  color: var(--orange);
  margin: 0 0 0.8rem 0;
  font-size: 1.2rem;
}

.project-description {
  color: var(--white);
  font-size: 1rem;
  margin-bottom: 1.2rem;
}

.project-meta {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.project-date {
  display: flex;
  gap: 0.5rem;
}

.date-label {
  color: var(--orange-light);
  font-weight: bold;
  font-family: "Courier New", monospace;
  min-width: 60px;
}

.date-value {
  color: var(--white);
  font-family: "Courier New", monospace;
  letter-spacing: 1px;
}

@media (max-width: 768px) {
  .project-details {
    flex-direction: column;
    align-items: center;
  }

  .project-image {
    margin-bottom: 1rem;
  }

  /* Responsive pour les filtres */
  .projects-filters {
    padding: 0.8rem;
  }

  .search-input {
    max-width: 100%;
  }



  /* Responsive pour la vue compacte */
  .projects-list.compact-view {
    grid-template-columns: 1fr;
    gap: 0.8rem;
  }

  .projects-list.compact-view .project-item {
    padding: 0.6rem;
  }

  .filter-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 0.8rem;
  }

  .filter-results {
    text-align: center;
  }

  .view-toggle {
    justify-content: center;
  }
}

/* === Certifications === */

.certifications-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  justify-content: center;
}

.certification-item {
  display: flex;
  align-items: center; /* Aligne verticalement l'image et le texte */
  gap: 1rem; /* Espace entre l'image et le texte */
  background: var(--gray-medium); /* Changé de --gray-light */
  padding: 1rem;
  border-radius: 0; /* Style pixel */
  box-shadow: 0 2px 0 var(--black); /* Style pixel */
  border: 1.5px solid var(--orange); /* Ajout bordure */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  flex-basis: calc(50% - 1rem); /* Deux éléments par ligne sur les grands écrans */
  max-width: 400px; /* Limite la largeur maximale */
}

.certification-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.certification-badge {
  max-width: 60px; /* Taille de la miniature */
  height: auto;
  object-fit: contain;
  flex-shrink: 0; /* Empêche l'image de rétrécir */
}

.certification-item p {
  margin: 0;
  color: var(--white); /* Changé de --black */
  flex-grow: 1; /* Permet au texte de prendre l'espace restant */
}

/* Responsive pour les certifications */
@media (max-width: 768px) {
  .certification-item {
    flex-basis: 100%; /* Un élément par ligne sur les petits écrans */
    max-width: none;
  }
}

/* Contact */
.contact-form {
  display: flex;
  flex-direction: column;
  gap: 0.7rem;
  max-width: 350px;
  margin: 1.5rem auto 0 auto;
  background: var(--gray-medium);
  padding: 1.2rem 1rem;
  border-radius: 0;
  box-shadow: 0 2px 0 var(--black);
  border: 2px solid var(--orange);
}
.contact-form label {
  color: var(--orange-light);
  font-weight: bold;
  font-size: 1rem;
}
.contact-form input,
.contact-form textarea {
  font-family: inherit;
  font-size: 1rem;
  border: 2px solid var(--gray-dark);
  border-radius: 0;
  padding: 0.4em 0.7em;
  background: var(--black);
  color: var(--white);
  outline: none;
  transition: border 0.2s;
  resize: none;
}
.contact-form input:focus,
.contact-form textarea:focus {
  border: 2px solid var(--orange-light);
}
.contact-form button {
  background: var(--orange);
  color: var(--black);
  font-weight: bold;
  border: 2px solid var(--orange-light);
  border-radius: 0;
  padding: 0.5em 1.2em;
  font-size: 1.1rem;
  cursor: pointer;
  box-shadow: none;
  transition: background 0.2s, color 0.2s;
}
.contact-form button:hover,
.contact-form button:focus {
  background: var(--orange-light);
  color: var(--white);
  border-color: var(--white);
}

.contact-links {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 1.2rem;
}
.contact-link {
  color: var(--orange);
  background: var(--gray-dark);
  border: 2px solid var(--orange-light);
  border-radius: 0;
  padding: 0.4em 1em;
  text-decoration: none;
  font-weight: bold;
  transition: background 0.2s, color 0.2s, border 0.2s;
  box-shadow: none;
}
.contact-link:hover,
.contact-link:focus {
  background: var(--orange);
  color: var(--black);
  border-color: var(--white);
}

.mascotte-contact {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}
#fox-mascot-contact {
  image-rendering: pixelated;
  width: 64px;
  height: 64px;
  animation: fox-blink 4s infinite;
}

/* Responsive */
@media (max-width: 900px) {
  .section {
    max-width: 98vw;
    padding: 2.5rem 0.5rem 2rem 0.5rem;
  }
  .navbar ul {
    margin-left: 0;
  }
  .sidebar-toggle, .theme-toggle {
    font-size: 1rem;
    padding: 0.2em 0.6em;
  }

  /* Ajustement de la grille compacte pour les écrans moyens */
  .projects-list.compact-view {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 0.8rem;
  }
}

/* Pixel scanline effect (optional, subtle) */
/* Scanline effect removed for a cleaner look */

/* Section reveal animation */
.section {
  opacity: 0;
  transform: translateY(40px) scale(0.98);
  transition: opacity 0.7s cubic-bezier(0.4, 1.4, 0.6, 1),
    transform 0.7s cubic-bezier(0.4, 1.4, 0.6, 1);
}
.section.visible {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* === Volet Latéral (Sidebar) === */
.sidebar-definitions {
  position: fixed;
  left: 0;
  top: 0;
  height: 100%;
  width: 250px; /* Largeur du volet */
  background: var(--gray-medium);
  border-right: 4px solid var(--orange);
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.2);
  padding: 1rem;
  padding-top: 4rem; /* Espace pour le bouton */
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
  z-index: 110; /* Au-dessus de la navbar */
  overflow-y: auto; /* Pour le défilement si le contenu est long */
}

.sidebar-definitions.open {
  transform: translateX(0);
}

.sidebar-definitions h2 {
  color: var(--orange-light);
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  text-align: center;
}

.definitions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.definitions-list li {
  margin-bottom: 1rem;
  color: var(--white);
  font-size: 0.95rem;
}

.definitions-list li strong {
  color: var(--orange);
  display: block;
  margin-bottom: 0.3rem;
}

.sidebar-toggle {
  background: transparent;
  color: var(--orange);
  border: none;
  border-radius: 0;
  width: 44px;
  height: 44px;
  font-size: 1.1rem;
  cursor: pointer;
  box-shadow: none;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin-left: 0.5rem;
  transition: background 0.18s, color 0.18s;
  position: relative;
  z-index: 120;
}
.sidebar-toggle:hover, .sidebar-toggle:focus {
  background: var(--orange);
  color: var(--black);
}

.sidebar-toggle .hamburger {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 26px;
  height: 18px;
  position: relative;
}
.sidebar-toggle .hamburger span {
  display: block;
  width: 100%;
  height: 3px;
  background: currentColor;
  margin: 2.5px 0;
  border-radius: 1px;
  transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
}

/* Animation hamburger en croix quand ouvert */
.sidebar-toggle.open .hamburger span:nth-child(1) {
  transform: translateY(7.5px) rotate(45deg);
}
.sidebar-toggle.open .hamburger span:nth-child(2) {
  opacity: 0;
}
.sidebar-toggle.open .hamburger span:nth-child(3) {
  transform: translateY(-7.5px) rotate(-45deg);
}

/* Pour le light theme, la couleur est héritée de currentColor donc reste visible */
body.light-theme .sidebar-toggle {
  color: var(--orange);
}

/* Supprime les anciens styles qui cachaient la barre centrale */
.sidebar-toggle .hamburger::before,
.sidebar-toggle .hamburger::after {
  display: none;
}

/* Ajustement du contenu principal quand le volet est ouvert (optionnel) */
body.sidebar-open .navbar,
body.sidebar-open .section,
body.sidebar-open .footer {
  /* Optionnel: décaler le contenu principal */
  /* margin-left: 250px; */
  /* transition: margin-left 0.3s ease-in-out; */
}

/* === Mini Carte d'Identité === */
.mini-id-card {
  display: flex;
  align-items: center;
  background: var(--gray-medium);
  border: 2px solid var(--orange-light);
  border-radius: 3px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
  max-width: 420px;
  margin: 0 auto 2.2rem auto;
  padding: 0.7em 1.2em;
  gap: 1.1em;
}
.flag-fr {
  display: inline-block;
  width: 30px;
  height: 22px;
  border-radius: 2px;
  border: 1px solid #bbb;
  background: linear-gradient(90deg, #0055a4 33%, #fff 33% 66%, #ef4135 66%);
  flex-shrink: 0;
}
.id-info {
  display: flex;
  flex-direction: column;
  gap: 0.1em;
}
.id-name {
  color: var(--orange);
  font-weight: bold;
  font-size: 1.1rem;
  font-family: inherit;
}
.id-title {
  color: var(--white);
  font-size: 1rem;
}
.id-city {
  color: var(--orange-light);
  font-size: 0.95rem;
  font-style: italic;
}
@media (max-width: 600px) {
  .mini-id-card {
    max-width: 95vw;
    flex-direction: row;
    padding: 0.6em 0.5em;
  }
  .id-info {
    font-size: 0.98rem;
  }
}

/* Footer */
.footer {
  text-align: center;
  padding: 1rem;
  color: var(--gray-medium);
  font-size: 0.8rem;
  background: var(--black);
  border-top: 1px solid var(--gray-medium);
  margin-top: 2rem;
}

/* More info button */
.more-info-btn {
  display: inline-block;
  margin-top: 10px;
  padding: 5px 12px;
  background-color: var(--orange);
  color: var(--black);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
  text-decoration: none;
  font-weight: bold;
}

.more-info-btn:hover {
  background-color: var(--orange-light);
}

/* Projects page styles */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.project-card {
  background-color: var(--gray-medium);
  border-radius: 0;
  padding: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 2px solid var(--orange);
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  border-color: var(--orange-light);
}

.project-card h2 {
  margin-top: 0;
  color: var(--orange);
}

.project-placeholder-image {
  height: 180px;
  background-color: var(--gray-dark);
  border-radius: 0;
  margin: 15px 0;
  position: relative;
  overflow: hidden;
  border: 2px solid var(--orange-light);
}

.project-placeholder-image::after {
  content: "Image à venir";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--orange-light);
  font-style: italic;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 15px 0;
}

.tech-tag {
  background-color: var(--gray-dark);
  color: var(--orange-light);
  padding: 4px 8px;
  border-radius: 0;
  font-size: 12px;
  border: 1px solid var(--orange);
}

.project-link {
  display: inline-block;
  margin-top: 10px;
  color: var(--orange);
  text-decoration: none;
  font-weight: 500;
  background: var(--gray-dark);
  padding: 5px 10px;
  border: 1px solid var(--orange-light);
}

.project-link:hover {
  background: var(--orange);
  color: var(--black);
  text-decoration: none;
}

.projets-details {
  padding: 80px 5%;
  max-width: 1200px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .projects-grid {
    grid-template-columns: 1fr;
  }

  .project-card {
    padding: 15px;
  }

  .project-placeholder-image {
    height: 150px;
  }
}

.footer .datetime {
  margin-top: 0.5rem;
  font-family: "Courier New", monospace;
  color: var(--gray-medium);
  opacity: 0.8;
}

/* Helper class */
.hidden {
  display: none !important; /* Ensure this hides the element */
}

/* Chatbot Styles */
.chatbot-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.chatbot-button {
  background-color: var(--orange); /* Use theme variable */
  color: var(--black); /* Text color for contrast */
  border: 2px solid var(--orange-light); /* Use theme variable */
  border-radius: 0; /* Pixel style */
  width: 50px; /* Slightly smaller */
  height: 50px;
  font-size: 24px;
  cursor: pointer;
  box-shadow: 0 2px 0 var(--black); /* Pixel style shadow */
  transition: background-color 0.2s, transform 0.2s;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chatbot-button:hover {
  background-color: var(--orange-light);
  transform: scale(1.05); /* Subtle hover effect */
}

.chatbot-window {
  width: 350px; /* Slightly wider */
  height: 450px; /* Slightly taller */
  background-color: var(--gray-dark); /* Use theme variable */
  border: 3px solid var(--orange); /* Use theme variable */
  border-radius: 0; /* Pixel style */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* Removed hidden class here, handled by JS */
}

/* .chatbot-window.hidden is handled by JS adding/removing the generic .hidden class */

.chatbot-header {
  background-color: var(--orange); /* Use theme variable */
  color: var(--black); /* Text color for contrast */
  padding: 8px 12px; /* Adjust padding */
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid var(--orange-light); /* Use theme variable */
  font-size: 1.1rem;
}

.chatbot-header div {
  /* Container for buttons */
  display: flex;
  align-items: center;
  gap: 5px; /* Space between buttons */
}

.chatbot-clear-button {
  background: none;
  border: none;
  color: var(--black);
  font-size: 20px; /* Slightly smaller than close */
  cursor: pointer;
  padding: 0 5px;
  line-height: 1; /* Ensure icon aligns well */
}
.chatbot-clear-button:hover {
  color: var(--gray-dark);
}

.chatbot-close-button {
  background: none;
  border: none;
  color: var(--black); /* Match header text */
  font-size: 24px; /* Larger */
  cursor: pointer;
  padding: 0 5px;
}
.chatbot-close-button:hover {
  color: var(--gray-dark);
}

.chatbot-messages {
  flex-grow: 1;
  padding: 10px;
  overflow-y: auto;
  background-color: var(--gray-medium); /* Use theme variable */
  display: flex;
  flex-direction: column;
  gap: 10px; /* Slightly more space */
  border-bottom: 2px solid var(--orange); /* Separator */
}

.message {
  padding: 8px 12px;
  border-radius: 4px; /* Minimal rounding */
  max-width: 85%;
  word-wrap: break-word;
  font-size: 0.95rem; /* Slightly smaller font */
  line-height: 1.4;
  box-shadow: 1px 1px 0px rgba(0, 0, 0, 0.2); /* Subtle shadow */
}

.message.bot {
  background-color: var(--white); /* Use theme variable */
  color: var(--black); /* Use theme variable */
  align-self: flex-start;
  border: 1px solid var(--gray-dark);
  /* Removed bubble radius */
}

.message.user {
  background-color: var(--orange-light); /* Use theme variable */
  color: var(--black); /* Use theme variable */
  align-self: flex-end;
  border: 1px solid var(--orange);
  /* Removed bubble radius */
}

.chatbot-input {
  display: flex;
  padding: 10px;
  background-color: var(--gray-dark); /* Match window bg */
}

.chatbot-input input {
  flex-grow: 1;
  padding: 8px 10px;
  border: 2px solid var(--orange-light); /* Use theme variable */
  border-radius: 0; /* Pixel style */
  margin-right: 8px;
  background-color: var(--black); /* Use theme variable */
  color: var(--white); /* Use theme variable */
  font-family: inherit; /* Ensure monospace font */
  font-size: 1rem;
  outline: none;
}
.chatbot-input input:focus {
  border-color: var(--white);
}

.chatbot-input button {
  padding: 8px 15px;
  background-color: var(--orange); /* Use theme variable */
  color: var(--black); /* Use theme variable */
  border: 2px solid var(--orange-light); /* Use theme variable */
  border-radius: 0; /* Pixel style */
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
  font-family: inherit; /* Ensure monospace font */
  font-weight: bold;
  font-size: 1rem;
}

.chatbot-input button:hover {
  background-color: var(--orange-light);
  border-color: var(--white);
}

/* Ensure text color is readable on inputs */
input::placeholder {
  color: var(--gray-medium); /* Adjust placeholder color */
  opacity: 0.8;
}

/* --- Added Start --- */
@keyframes slideInUp {
  from {
    transform: translateY(100%) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes slideOutDown {
  from {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  to {
    transform: translateY(50%) scale(0.9);
    opacity: 0;
  }
}

.chatbot-window.opening {
  animation: slideInUp 0.4s ease-out forwards;
}

.chatbot-window.closing {
  animation: slideOutDown 0.3s ease-in forwards;
}
/* --- Added End --- */

.theme-toggle {
  background: transparent;
  color: var(--orange);
  border: none;
  border-radius: 0;
  font-size: 1.1rem;
  padding: 0.3em 0.8em;
  cursor: pointer;
  box-shadow: none;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 0.5rem;
  transition: background 0.18s, color 0.18s;
}
.theme-toggle:hover, .theme-toggle:focus {
  background: var(--orange);
  color: var(--black);
}

/* Ajouts pour améliorer le thème clair */
body.light-theme .section {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid #ccc;
}

body.light-theme .navbar {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

body.light-theme .project-card {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}

body.light-theme .project-card:hover {
  box-shadow: 0 5px 15px rgba(255, 127, 42, 0.15);
}

body.light-theme .contact-form {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
}

body.light-theme .mini-id-card {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}

body.light-theme .skill-bar {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

body.light-theme h1 {
  text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.1);
}

body.light-theme .skill-name {
  text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.1);
}

/* Correction de contraste pour le thème clair */
body.light-theme .navbar a {
  color: #e65c00;
}

body.light-theme .navbar a:hover,
body.light-theme .navbar a:focus {
  color: var(--black);
}

/* Plus de profondeur aux projets */
body.light-theme .projects-list {
  perspective: 800px;
}

body.light-theme .project-card:hover {
  transform: scale(1.06) rotate(-1deg) translateZ(10px);
}
