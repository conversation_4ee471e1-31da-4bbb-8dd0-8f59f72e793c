<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RedFox – Projets</title>
    <link rel="stylesheet" href="../style.css" />
  </head>
  <body>
    <nav class="navbar">
      <button
        class="sidebar-toggle"
        aria-label="Ouvrir/fermer le menu"
        aria-controls="sidebar-definitions"
        aria-expanded="false"
      >
        <span class="hamburger"><span></span><span></span><span></span></span>
      </button>
      <ul>
        <li><a href="../index.html#accueil">Accueil</a></li>
        <li><a href="../index.html#a-propos">À propos</a></li>
        <li><a href="../index.html#competences">Compétences</a></li>
        <li><a href="../index.html#projets">Projets</a></li>
        <li><a href="../index.html#certifications">Certifications</a></li>
        <li><a href="../index.html#contact">Contact</a></li>
      </ul>
      <button id="theme-toggle" class="theme-toggle" aria-label="Changer de thème" title="Changer de thème">
        <span class="theme-icon">🌙</span>
      </button>
    </nav>
    <aside class="sidebar-definitions" id="sidebar-definitions">
      <h2>Définitions</h2>
      <ul class="definitions-list">
        <li><strong>IA Générative</strong>Technologie d'intelligence artificielle capable de créer du contenu original (texte, images, sons) en s'inspirant de données existantes. Exemples : ChatGPT, DALL-E, Midjourney.</li>
        <li><strong>Machine Learning</strong>Branche de l'IA permettant aux ordinateurs d'apprendre à partir de données sans être explicitement programmés. Fondement des systèmes prédictifs et de reconnaissance de motifs.</li>
        <li><strong>Développeur Fullstack</strong>Professionnel maîtrisant à la fois le développement frontend (interface utilisateur) et backend (serveur, base de données), capable de créer des applications web complètes.</li>
        <li><strong>Intégrateur Cloud</strong>Expert qui implémente, configure et optimise les solutions cloud pour les entreprises, assurant la migration et l'interconnexion des services.</li>
        <li><strong>Pixel Art</strong>Style graphique numérique où les images sont créées au niveau du pixel, souvent avec une palette de couleurs limitée, évoquant l'esthétique des jeux vidéo rétro.</li>
        <li><strong>Azure</strong>Plateforme cloud de Microsoft offrant des services d'hébergement, de calcul, d'IA et d'analyse de données pour les entreprises et développeurs.</li>
        <li><strong>AZ-900</strong>Certification Microsoft Azure Fundamentals qui valide les connaissances de base sur les services cloud, les modèles de tarification et la conformité dans Azure.</li>
      </ul>
    </aside>

    <section class="section projets-details">
      <h1>Projets</h1>

      <!-- Filtres des projets -->
      <div class="projects-filters" role="region" aria-label="Filtres des projets">
        <div class="filter-search">
          <label for="project-search" class="sr-only">Rechercher un projet</label>
          <input
            type="text"
            id="project-search"
            class="search-input"
            placeholder="Rechercher un projet..."
            aria-describedby="search-help"
          />
          <span id="search-help" class="sr-only">Tapez pour filtrer les projets par nom ou description</span>
        </div>

        <div class="filter-results" aria-live="polite" aria-atomic="true">
          <span id="results-count">3 projets affichés</span>
        </div>
      </div>

      <div class="projects-grid">
        <!-- Placeholder for FoxGen -->
        <div class="project-card" data-tags="Python,IA Générative,Pixel Art" data-search-text="foxgen générateur images ia pixel art faune sauvage">
          <h2>FoxGen</h2>
          <div class="project-placeholder-image"></div>
          <p>Générateur d'images IA inspiré du pixel art et de la faune sauvage.</p>
          <div class="project-tech">
            <span class="tech-tag">Python</span>
            <span class="tech-tag">IA Générative</span>
            <span class="tech-tag">Pixel Art</span>
          </div>
          <a href="#" class="project-link">Voir le projet</a>
        </div>

        <!-- Placeholder for Text2Quest -->
        <div class="project-card" data-tags="JavaScript,IA Générative,UX Design" data-search-text="text2quest aventures textuelles interactives ia générative jeu ux design">
          <h2>Text2Quest</h2>
          <div class="project-placeholder-image"></div>
          <p>Création d'aventures textuelles interactives grâce à l'IA générative.</p>
          <div class="project-tech">
            <span class="tech-tag">JavaScript</span>
            <span class="tech-tag">IA Générative</span>
            <span class="tech-tag">UX Design</span>
          </div>
          <a href="#" class="project-link">Voir le projet</a>
        </div>

        <!-- Placeholder for RetroSynth -->
        <div class="project-card" data-tags="JavaScript,Web Audio API,IA Générative" data-search-text="retrosynth synthétiseur sons rétro ia jeux vidéo pixel art audio">
          <h2>RetroSynth</h2>
          <div class="project-placeholder-image"></div>
          <p>Synthétiseur de sons rétro piloté par IA pour jeux vidéo pixel art.</p>
          <div class="project-tech">
            <span class="tech-tag">JavaScript</span>
            <span class="tech-tag">Web Audio API</span>
            <span class="tech-tag">IA Générative</span>
          </div>
          <a href="#" class="project-link">Voir le projet</a>
        </div>
      </div>
    </section>

    <footer class="footer">
      <p>&copy; 2025 RedFox. Tous droits réservés.</p>
      <p class="datetime"></p>
    </footer>

    <script src="../script.js" defer></script>
  </body>
</html>